# Debian VPS 防火墙一键配置指南

## 🎯 功能概述
一键配置Debian VPS防火墙，实现SSH安全访问控制和端口管理。

### 核心功能
- **SSH安全控制** - 只允许指定IP访问SSH
- **端口白名单** - 仅开放必要端口
- **DDoS防护** - 拒绝未授权连接
- **开机自启** - 重启后自动恢复配置

## 📁 文件准备
需要准备两个文件：
- `firewall.sh` - 防火墙配置脚本
- `.env` - 配置文件（包含允许的IP和端口）

## 🚀 快速部署

### 1. 上传文件到服务器
```bash
scp firewall.sh .env root@VPS_IP:/var/srv/iptables/
```

### 2. 登录服务器并进入目录
```bash
ssh root@VPS_IP
cd /var/srv/iptables/
```

### 3. 修改配置文件
```bash
nano .env
```
⚠️ **重要**：确保当前IP地址在允许访问列表中！

### 4. 执行配置脚本
```bash
chmod +x firewall.sh && ./firewall.sh
```

## ✅ 验证配置

### 查看防火墙规则
```bash
iptables -L -n -v
```

### 查看系统状态
```bash
# 查看当前连接
netstat -tulnp

# 查看服务状态
systemctl status iptables-restore.service

# 查看备份文件
ls -la /root/iptables_backup_*.txt
```

## 🔧 配置修改

### 更新配置
```bash
nano .env              # 修改配置
./firewall.sh          # 重新应用
```

## 🆘 紧急恢复

如果配置错误导致无法连接，通过VPS控制台执行：

### 方法1：清除所有规则
```bash
iptables -F
iptables -P INPUT ACCEPT
iptables -P OUTPUT ACCEPT
iptables -P FORWARD ACCEPT
```

### 方法2：恢复备份
```bash
iptables-restore < /root/iptables_backup_最新时间戳.txt
```

### 方法3：停止防火墙服务
```bash
systemctl stop iptables-restore.service
```

## ⚠️ 注意事项

1. **执行前必须确认当前IP在允许列表中**
2. **建议先在测试环境验证**
3. **脚本会自动备份原有配置**
4. **配置完成后测试SSH访问和端口连通性**

## 📊 配置完成后效果

- ✅ 系统环境就绪（自动安装iptables）
- ✅ SSH访问安全（仅允许指定IP）
- ✅ 服务端口开放（按需配置）
- ✅ DDoS防护（拒绝未授权连接）
- ✅ 出站正常（不影响服务器访问外网）
- ✅ 开机自启（重启后自动恢复）
