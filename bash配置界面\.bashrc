# 在这里粘贴完整的配置内容
# ~/.bashrc: executed by bash(1) for non-login shells.

# Note: PS1 and umask are already set in /etc/profile. You should not
# need this unless you want different defaults for root.

# ===========================================
# 基本设置
# ===========================================

# 检测是否为 SFTP 会话，如果是则跳过所有输出
if [[ -n "$SSH_ORIGINAL_COMMAND" ]] && [[ "$SSH_ORIGINAL_COMMAND" == *"sftp"* ]]; then
    return
fi

# 检测非交互式会话
case "$-" in
    *i*) ;;      # 交互式，继续执行
    *) return;;  # 非交互式，直接返回
esac

umask 022

# 历史设置
export HISTSIZE=5000
export HISTFILESIZE=10000
export HISTCONTROL=ignoredups:erasedups
export HISTTIMEFORMAT="[%F %T] "
shopt -s histappend

# 其他 bash 选项
shopt -s checkwinsize
shopt -s cdspell
shopt -s cmdhist
shopt -s dotglob
shopt -s expand_aliases
shopt -s extglob

# ===========================================
# 颜色设置
# ===========================================
# 启用颜色支持
export LS_OPTIONS='--color=auto'
export CLICOLOR=1
eval "$(dircolors -b)"

# ===========================================
# 自定义提示符 (美观的 PS1)
# ===========================================
# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 设置漂亮的提示符
if [ "$EUID" -eq 0 ]; then
    # Root 用户 - 也使用绿色提示
    PS1="${GREEN}┌─[${WHITE}\u${GREEN}@${WHITE}\h${GREEN}]─[${CYAN}\w${GREEN}]\n${GREEN}└─${WHITE}# ${NC}"
else
    # 普通用户 - 绿色提示
    PS1="${GREEN}┌─[${WHITE}\u${GREEN}@${WHITE}\h${GREEN}]─[${CYAN}\w${GREEN}]\n${GREEN}└─${WHITE}$ ${NC}"
fi

# ===========================================
# 别名设置
# ===========================================
# 基本别名
alias ls='ls $LS_OPTIONS'
alias ll='ls $LS_OPTIONS -alF'
alias la='ls $LS_OPTIONS -A'
alias l='ls $LS_OPTIONS -CF'
alias lt='ls $LS_OPTIONS -altr'
alias lh='ls $LS_OPTIONS -alh'

# 安全别名
alias rm='rm -i'
alias cp='cp -i'
alias mv='mv -i'
alias ln='ln -i'

# 目录导航
alias ..='cd ..'
alias ...='cd ../..'
alias ....='cd ../../..'
alias .....='cd ../../../..'
alias ~='cd ~'
alias -- -='cd -'

# 系统信息
alias df='df -h'
alias du='du -h'
alias free='free -h'
alias ps='ps auxf'
alias psg='ps aux | grep -v grep | grep -i -e VSZ -e'
alias top='htop'
alias ports='netstat -tulanp'

# 网络
alias ping='ping -c 5'
alias wget='wget -c'
alias curl='curl -L'

# 时间和日期
alias now='date +"%T"'
alias nowtime=now
alias nowdate='date +"%d-%m-%Y"'

# 进程管理
alias psmem='ps auxf | sort -nr -k 4'
alias psmem10='ps auxf | sort -nr -k 4 | head -10'
alias pscpu='ps auxf | sort -nr -k 3'
alias pscpu10='ps auxf | sort -nr -k 3 | head -10'

# 你的自定义别名
alias sb=/usr/local/bin/sing-box
alias sing-box=/usr/local/bin/sing-box

# 快捷编辑
alias bashrc='nano ~/.bashrc'
alias reload='source ~/.bashrc'

# 系统状态
alias status='get_system_status'

# ===========================================
# 环境变量
# ===========================================
export EDITOR=nano
export VISUAL=nano
export PAGER=less
export BROWSER=w3m
export TERM=xterm-256color

# Python 设置
export PYTHONPATH="$PYTHONPATH:$HOME/.local/lib/python3.11/site-packages"
export PIP_REQUIRE_VIRTUALENV=false

# ===========================================
# 路径设置
# ===========================================
# 添加本地 bin 目录到 PATH
export PATH="$HOME/.local/bin:$PATH"
export PATH="$PATH:/usr/local/bin"

# 如果存在，加载本地环境
if [ -f "$HOME/.local/bin/env" ]; then
    . "$HOME/.local/bin/env"
fi

# ===========================================
# 函数定义
# ===========================================
# 快速查找文件
function ff() {
    find . -type f -name '*'"$1"'*' 2>/dev/null
}

# 快速查找目录
function fd() {
    find . -type d -name '*'"$1"'*' 2>/dev/null
}

# 创建目录并进入
function mkcd() {
    mkdir -p "$1" && cd "$1"
}

# 显示进程树
function pstree() {
    ps auxf | grep -v grep | grep "$1"
}

# 快速解压
function extract() {
    if [ -f "$1" ]; then
        case "$1" in
            *.tar.bz2)   tar xjf "$1"     ;;
            *.tar.gz)    tar xzf "$1"     ;;
            *.bz2)       bunzip2 "$1"     ;;
            *.rar)       unrar x "$1"     ;;
            *.gz)        gunzip "$1"      ;;
            *.tar)       tar xf "$1"      ;;
            *.tbz2)      tar xjf "$1"     ;;
            *.tgz)       tar xzf "$1"     ;;
            *.zip)       unzip "$1"       ;;
            *.Z)         uncompress "$1"  ;;
            *.7z)        7z x "$1"        ;;
            *)           echo "'$1' cannot be extracted via extract()" ;;
        esac
    else
        echo "'$1' is not a valid file"
    fi
}

# 系统更新函数
function sysupdate() {
    echo "Updating system..."
    apt update && apt upgrade -y
    echo "System updated successfully!"
}

# 获取系统状态信息函数
function get_system_status() {
    # 获取当前时间 (格式: YYMMDD HH:MM:SS)
    local current_time=$(date +"%y%m%d %H:%M:%S")

    # 获取内存使用率
    local mem_usage=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')

    # 获取磁盘使用率 (根分区)
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')

    echo "${current_time} Mem${mem_usage}% Disk${disk_usage}%"
}

# ===========================================
# 欢迎信息
# ===========================================
# 只在交互式会话中显示欢迎信息（SFTP 检测已在文件开头处理）
if [ "$PS1" ]; then
    echo -e "${GREEN}欢迎使用 Bash!${NC}"
    echo -e "${YELLOW}系统信息:${NC}"
    echo -e "  主机名: ${CYAN}$(hostname)${NC}"
    echo -e "  时间: ${CYAN}$(date)${NC}"
    echo -e "  运行时间: ${CYAN}$(uptime -p)${NC}"
    echo -e "  用户: ${CYAN}$(whoami)${NC}"
    echo -e "  工作目录: ${CYAN}$(pwd)${NC}"
    echo -e "  ${PURPLE}$(get_system_status)${NC}"
    echo ""
fi

# ===========================================
# 补全设置
# ===========================================
if ! shopt -oq posix; then
    if [ -f /usr/share/bash-completion/bash_completion ]; then
        . /usr/share/bash-completion/bash_completion
    elif [ -f /etc/bash_completion ]; then
        . /etc/bash_completion
    fi
fi

# ===========================================
# 自定义设置结束
# ===========================================
