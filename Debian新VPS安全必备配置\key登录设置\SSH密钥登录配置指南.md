# SSH密钥登录配置指南

## 📋 概述
将Debian服务器从密码登录改为SSH密钥登录，提高安全性。

## 🎯 目标
- **当前状态**: root用户 + 密码登录
- **目标状态**: root用户 + SSH密钥登录
- **禁用密码登录**: 提高安全性

---

## 🔧 操作步骤

### 步骤1: 准备密钥文件
确保你已经生成了密钥对：
- **私钥**: `claw_ed25519` (保存在本地)
- **公钥**: `claw_ed25519.pub` (需要上传到服务器)

### 步骤2: 连接到服务器
```bash
# 使用密码登录到服务器
ssh root@*************
```

### 步骤3: 创建SSH目录和文件
```bash
# 创建.ssh目录 (如果不存在)
mkdir -p ~/.ssh

# 设置.ssh目录权限
chmod 700 ~/.ssh

# 创建authorized_keys文件 (如果不存在)
touch ~/.ssh/authorized_keys

# 设置authorized_keys文件权限
chmod 600 ~/.ssh/authorized_keys
```

### 步骤4: 添加公钥到服务器

#### 方法A: 直接编辑文件
```bash
# 编辑authorized_keys文件
nano ~/.ssh/authorized_keys
```
然后将你的公钥内容粘贴进去，格式如下：
```
ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJoovR2T9195EaVVANvPr68cfljDeia4VjsEDk4g6cO8 claw server - Generated on 2025-08-05 20:31:16
```

#### 方法B: 使用echo命令
```bash
# 直接添加公钥 (替换为你的实际公钥内容)
echo "ssh-ed25519 AAAAC3NzaC1lZDI1NTE5AAAAIJoovR2T9195EaVVANvPr68cfljDeia4VjsEDk4g6cO8 claw server - Generated on 2025-08-05 20:31:16" >> ~/.ssh/authorized_keys
```

### 步骤5: 验证密钥登录
**⚠️ 重要：不要关闭当前SSH连接！**

打开新的终端窗口测试密钥登录：
```bash
# 测试密钥登录 (替换为你的实际文件路径和服务器IP)
ssh -i /path/to/claw_ed25519 root@*************
```

如果成功登录，说明密钥配置正确。

### 步骤6: 配置SSH服务器 (禁用密码登录)
```bash
# 1. 备份SSH配置文件
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

# 2. 备份并删除可能覆盖配置的其他文件
cp -r /etc/ssh/sshd_config.d /etc/ssh/sshd_config.d.backup
rm -f /etc/ssh/sshd_config.d/*

# 3. 编辑SSH配置
nano /etc/ssh/sshd_config
```

**nano编辑器操作：清空文件内容，然后粘贴新配置**
```bash
# 清空文件内容的快捷键
Alt+A      # 开始标记选择
Ctrl+End   # 移动到文件末尾 (或 Alt+/)
Ctrl+K     # 删除标记的内容

# 然后粘贴以下完整配置
```

```bash
# SSH服务器配置文件 - 精简版
# 仅保留核心必要配置

# 基本设置
Port 22
PermitRootLogin yes

# 密钥认证 (启用)
PubkeyAuthentication yes
AuthorizedKeysFile .ssh/authorized_keys

# 密码认证 (完全禁用)
PasswordAuthentication no
PermitEmptyPasswords no
ChallengeResponseAuthentication no

# 仅允许公钥认证
AuthenticationMethods publickey

# 安全设置
MaxAuthTries 3
UseDNS no

# 必要的系统设置
UsePAM yes
Subsystem sftp /usr/lib/openssh/sftp-server
```

### 步骤7: 重启SSH服务
```bash
# 重启SSH服务
systemctl restart sshd

# 检查SSH服务状态
systemctl status sshd
```

### 步骤8: 最终测试
```bash
# 测试密钥登录 (应该成功)
ssh -i claw_ed25519 root@your_server_ip

# 测试密码登录 (应该被拒绝)
ssh root@your_server_ip
# 期望结果: Permission denied (publickey)
```

---

## 🔍 故障排除

### 仍然可以密码登录
```bash
# 1. 检查配置是否生效
sshd -T | grep -E "(passwordauthentication|authenticationmethods)"

# 2. 删除覆盖配置文件
rm -f /etc/ssh/sshd_config.d/*

# 3. 确保配置正确
nano /etc/ssh/sshd_config
# 确认包含: AuthenticationMethods publickey

# 4. 重启服务
systemctl restart sshd
```

### 密钥登录失败
```bash
# 检查权限
chmod 700 ~/.ssh
chmod 600 ~/.ssh/authorized_keys

# 查看日志
journalctl -u ssh -n 10
```

---

## 📱 Termius配置

导入私钥文件 `claw_ed25519`，设置用户名为 `root`
