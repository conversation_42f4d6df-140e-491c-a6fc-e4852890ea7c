```bash
apt update && apt full-upgrade -y && apt install -y \
  autoconf automake \
  bash-completion bat bison build-essential \
  ca-certificates cmake curl cmatrix \
  dmidecode dnsutils \
  fd-find flex fzf \
  g++ gcc git git-lfs gawk gnupg \
  htop \
  iputils-ping iptables iptables-persistent \
  jq \
  less libbz2-dev libboost-dev libc6-dev libclang-dev libcurl4-openssl-dev \
  libeigen3-dev libffi-dev libfontconfig-dev libgdbm-dev libgit2-dev \
  liblzma-dev libncurses5-dev libpcre2-dev libpcre3-dev libprotobuf-dev \
  libreadline-dev libssl-dev libstdc++-12-dev libsqlite3-dev libtool \
  libxml2-dev libxslt1-dev llvm-dev locales lsof \
  make man-db \
  nano neofetch net-tools nmap \
  openssh-client openssh-server \
  p7zip-full pipx pkg-config postgresql-client protobuf-compiler \
  python3-dev python3-full python3-pip python3-venv \
  ripgrep rsync \
  screen smartmontools sqlite3 sudo \
  tcpdump telnet tmux traceroute tree \
  unzip \
  vim \
  wget \
  zlib1g-dev
```

